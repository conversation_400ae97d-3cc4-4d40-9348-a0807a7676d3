import Mailgun from 'mailgun.js';
import { loadTemplate, processTemplate } from '../util/load-template';
import { ISurveyDoc } from './models/survey';
import { UserPasscodeModel } from './models/user-passcode';
import dbConnect from './mongodb';

const mailgun = new Mailgun(FormData);
const mailgunClient = process.env.MAILGUN_API_KEY
  ? mailgun.client({
      username: 'api',
      key: process.env.MAILGUN_API_KEY,
    })
  : null;

export default async function sendSimpleMessages(
  surveyDocs: ISurveyDoc[]
): Promise<undefined> {
  if (!mailgunClient) {
    console.warn('Mailgun client not initialized - skipping email sending');
    return;
  }

  await dbConnect();

  const htmlBody = loadTemplate('email-template.html');
  const textBody = loadTemplate('email-template.txt');

  // Group surveys by user email to send only one email per person
  const userSurveyMap = new Map<string, ISurveyDoc[]>();

  surveyDocs.forEach((surveyDoc) => {
    const email = surveyDoc.userEmail;
    // Skip surveys without valid email addresses
    if (!email || !email.trim()) {
      console.warn('Skipping survey with missing email:', surveyDoc._id);
      return;
    }

    if (!userSurveyMap.has(email)) {
      userSurveyMap.set(email, []);
    }
    userSurveyMap.get(email)!.push(surveyDoc);
  });

  // Send one email per unique user
  for (const [userEmail, userSurveys] of userSurveyMap) {
    try {
      // Get the user's passcode for this wave
      const firstSurvey = userSurveys[0];
      const userPasscode = await UserPasscodeModel.findOne({
        userEmail: userEmail,
        waveId: firstSurvey.waveId,
        isActive: true,
      });

      if (!userPasscode) {
        console.warn(
          `No passcode found for user ${userEmail} in wave ${firstSurvey.waveId}`
        );
        continue;
      }

      // Create login URL with auto-login parameters
      const baseUrl =
        process.env.SURVEY_LINK?.replace(/\/survey\/.*$/, '') ||
        'http://localhost:3000';
      const loginUrl = `${baseUrl}/login?email=${encodeURIComponent(
        userEmail
      )}&passcode=${encodeURIComponent(userPasscode.passcode)}`;

      await mailgunClient.messages.create(process.env.MAILGUN_DOMAIN!, {
        from: process.env.MAILGUN_FROM!,
        to: userEmail,
        subject: 'Your Agency Assessment Survey is Ready',
        html: processTemplate(htmlBody, {
          FirstName: firstSurvey.userName,
          SurveyLink: loginUrl,
        }),
        text: processTemplate(textBody, {
          FirstName: firstSurvey.userName,
          SurveyLink: loginUrl,
        }),
        'h:List-Unsubscribe': '<mailto:<EMAIL>>',
      });

      console.log(
        `Email sent to ${userEmail} for ${userSurveys.length} survey(s)`
      );
    } catch (error) {
      console.error(`Error sending email to ${userEmail}:`, error);
    }
  }

  return;
}
