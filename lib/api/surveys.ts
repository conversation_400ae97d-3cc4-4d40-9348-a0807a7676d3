'use server';

import dbConnect from '../mongodb';
import { Types } from 'mongoose';
import {
  SurveyModel,
  ISurveyImport,
  ISurveyDoc,
} from '../models/survey';
import { WaveModel } from '../models/wave';
import { UserPasscodeModel } from '../models/user-passcode';

export interface ISurveyVm {
  id: string;
  accountName: string;
  agencyName: string;
  agencyType: string;
  brand: string;
  country: string;
  region: string;
  assessmentType: string;
  userName: string;
  userEmail: string; // Can be empty for historical imports
  userStatus: string;
  inScope: string;
  notes: string;
  waveId: string;
}

// Map a Survey from Document to View Model
export async function fromSurveyDocToVm(
  doc: ISurveyDoc
): Promise<ISurveyVm> {
  return {
    id: doc._id.toString(),
    accountName: doc.accountName,
    agencyName: doc.agencyName,
    agencyType: doc.agencyType,
    brand: doc.brand,
    country: doc.country,
    region: doc.region,
    assessmentType: doc.assessmentType,
    userName: doc.userName,
    userEmail: doc.userEmail || '', // Handle undefined userEmail
    userStatus: doc.userStatus,
    inScope: doc.inScope,
    notes: doc.notes || '',
    waveId: doc.waveId.toString(),
  };
}

// Get Survey View Model by ID
export async function getSurveyVm(
  surveyId: string
): Promise<ISurveyVm | null> {
  await dbConnect();
  
  const surveyDoc: ISurveyDoc | null = await SurveyModel.findById(surveyId);

  if (!surveyDoc) {
    throw new Error('Invalid Survey ID');
  }

  return fromSurveyDocToVm(surveyDoc);
}

// Get surveys by user email and wave
export async function getSurveysByUser(
  userEmail: string,
  waveId: string
): Promise<ISurveyVm[]> {
  await dbConnect();

  // Use case-insensitive regex to match emails regardless of case
  const surveys = await SurveyModel.find({
    userEmail: { $regex: new RegExp(`^${userEmail.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}$`, 'i') },
    waveId
  });
  return Promise.all(surveys.map(fromSurveyDocToVm));
}

// Get surveys by passcode
export async function getSurveysByPasscode(
  passcode: string
): Promise<ISurveyVm[]> {
  await dbConnect();
  
  const userPasscode = await UserPasscodeModel.findOne({ 
    passcode, 
    isActive: true 
  });
  
  if (!userPasscode) {
    throw new Error('Invalid or expired passcode');
  }

  // Mark passcode as used if not already
  if (!userPasscode.usedAt) {
    await UserPasscodeModel.findByIdAndUpdate(userPasscode._id, {
      usedAt: new Date()
    });
  }

  return getSurveysByUser(userPasscode.userEmail, userPasscode.waveId.toString());
}

// Get latest wave Survey Documents
export async function getLatestWaveSurveyDocs(): Promise<ISurveyDoc[]> {
  await dbConnect();

  const latestWaveDoc = await WaveModel.findOne()
    .sort({ createdAt: -1 })
    .limit(1);

  if (!latestWaveDoc) {
    return [];
  }

  return await SurveyModel.find({
    waveId: latestWaveDoc._id.toString(),
  });
}

// Get unique values for filters
export async function getUniqueAgencies(): Promise<string[]> {
  await dbConnect();
  
  // Use aggregation to filter out seed waves
  const result = await SurveyModel.aggregate([
    {
      $lookup: {
        from: 'waves',
        localField: 'waveId',
        foreignField: '_id',
        as: 'wave'
      }
    },
    { $unwind: '$wave' },
    {
      $match: {
        'wave.status': { $ne: 'seed' }
      }
    },
    {
      $group: {
        _id: '$agencyName'
      }
    },
    {
      $sort: { _id: 1 }
    }
  ]);
  
  return result.map(item => item._id);
}

export async function getUniqueBrands(): Promise<string[]> {
  await dbConnect();
  
  // Use aggregation to filter out seed waves
  const result = await SurveyModel.aggregate([
    {
      $lookup: {
        from: 'waves',
        localField: 'waveId',
        foreignField: '_id',
        as: 'wave'
      }
    },
    { $unwind: '$wave' },
    {
      $match: {
        'wave.status': { $ne: 'seed' }
      }
    },
    {
      $group: {
        _id: '$brand'
      }
    },
    {
      $sort: { _id: 1 }
    }
  ]);
  
  return result.map(item => item._id);
}

export async function getUniqueRegions(): Promise<string[]> {
  await dbConnect();
  
  // Use aggregation to filter out seed waves
  const result = await SurveyModel.aggregate([
    {
      $lookup: {
        from: 'waves',
        localField: 'waveId',
        foreignField: '_id',
        as: 'wave'
      }
    },
    { $unwind: '$wave' },
    {
      $match: {
        'wave.status': { $ne: 'seed' }
      }
    },
    {
      $group: {
        _id: '$country'
      }
    },
    {
      $sort: { _id: 1 }
    }
  ]);
  
  return result.map(item => item._id);
}

export async function getUniqueCountries(): Promise<string[]> {
  await dbConnect();
  return await SurveyModel.distinct('country');
}

export async function getUniqueAgencyTypes(): Promise<string[]> {
  await dbConnect();

  // Use aggregation to filter out seed waves
  const result = await SurveyModel.aggregate([
    {
      $lookup: {
        from: 'waves',
        localField: 'waveId',
        foreignField: '_id',
        as: 'wave'
      }
    },
    { $unwind: '$wave' },
    {
      $match: {
        'wave.status': { $ne: 'seed' }
      }
    },
    {
      $group: {
        _id: '$agencyType'
      }
    },
    {
      $sort: { _id: 1 }
    }
  ]);

  return result.map(item => item._id);
}

// Get surveys by wave ID
export async function getSurveysByWave(waveId: string): Promise<ISurveyDoc[]> {
  await dbConnect();
  return await SurveyModel.find({ waveId });
}

// Helper function for wave creation (referenced in waves.ts)
export async function fromSurveyImportToInput(
  surveyImport: ISurveyImport,
  waveId: Types.ObjectId
) {
  return {
    ...surveyImport,
    waveId,
  };
} 