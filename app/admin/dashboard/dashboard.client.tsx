'use client';

import {
	Box,
	Typography,
	Select,
	MenuItem,
	InputLabel,
	CircularProgress,
	OutlinedInput,
	ListItemText,
	Checkbox,
	Button,
	Tooltip,
	Autocomplete,
	TextField,
} from '@mui/material';
import { useState, useEffect, useMemo, useCallback } from 'react';
import DashboardBarChart from './DashboardBarChart';
import DrilldownPanel from './DrilldownPanel';
import CommentsSentimentsPanel from './CommentsSentimentsPanel';
import WaveHistoryPanel from './WaveHistoryPanel';
import NPSHistoryPanel from './NPSHistoryPanel';
import SyncedHeightPanels from './SyncedHeightPanels';
import PanelSubheader from './PanelSubheader';
import { SelectChangeEvent } from '@mui/material';
import {
	RestartAlt as ResetIcon,
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import ElevatedFormControl from '../../_components/ElevatedFormControl';

interface WaveOption {
	label: string;  // Display label with date prefix
	value: string;  // Original wave name for filtering
}

interface FilterOptions {
	agencies: string[];
	brands: string[];
	regions: string[];
	waves: WaveOption[];
}

interface DashboardStats {
	totalResponses: number;
	averageScore: number;
	completionRate: number;
}

// Custom render function for wave selection to prevent horizontal overflow
const renderWaveValue = (selected: string[], filterOptions: FilterOptions) => {
	if (selected.length === 0) return '';

	// Map selected values to their display labels
	const selectedLabels = selected.map(value => {
		const waveOption = filterOptions.waves.find(w => w.value === value);
		return waveOption ? waveOption.label : value;
	});

	if (selectedLabels.length === 1) return selectedLabels[0];
	if (selectedLabels.length <= 3) return selectedLabels.join(', ');
	return `${selectedLabels.slice(0, 2).join(', ')} +${selectedLabels.length - 2} more`;
};

export default function DashboardClient() {
	const [selectedAgency, setSelectedAgency] = useState('All Agencies');
	const [selectedBrand, setSelectedBrand] = useState('All Brands');
	const [selectedRegion, setSelectedRegion] = useState('All Regions');
	const [selectedWaves, setSelectedWaves] = useState<string[]>([]);
	const [tempSelectedWaves, setTempSelectedWaves] = useState<string[]>([]); // Temporary state for wave selection
	const [filterOptions, setFilterOptions] = useState<FilterOptions>({
		agencies: ['All Agencies'],
		brands: ['All Brands'],
		regions: ['All Regions'],
		waves: [],
	});
	const [availableFilterOptions, setAvailableFilterOptions] =
		useState<FilterOptions>({
			agencies: ['All Agencies'],
			brands: ['All Brands'],
			regions: ['All Regions'],
			waves: [],
		});
	const [waveFilteredStats, setWaveFilteredStats] =
		useState<DashboardStats | null>(null);
	const [filteredStats, setFilteredStats] = useState<DashboardStats | null>(
		null
	);
	const [loading, setLoading] = useState(true);

	// Determine primary filter for drilldown
	const getPrimaryFilter = useCallback((): {
		type: 'agency' | 'brand' | 'region';
		value: string;
	} | null => {
		if (selectedAgency !== 'All Agencies') {
			return { type: 'agency', value: selectedAgency };
		} else if (selectedBrand !== 'All Brands') {
			return { type: 'brand', value: selectedBrand };
		} else if (selectedRegion !== 'All Regions') {
			return { type: 'region', value: selectedRegion };
		}
		return null;
	}, [selectedAgency, selectedBrand, selectedRegion]);

	// Memoize chart props to prevent unnecessary re-renders
	const chartProps = useMemo(
		() => ({
			agency: selectedAgency,
			brand: selectedBrand,
			region: selectedRegion,
			wave: selectedWaves.join(','),
			focusType: (getPrimaryFilter()?.type || 'all') as
				| 'agency'
				| 'brand'
				| 'region'
				| 'wave'
				| 'all',
			focusValue: getPrimaryFilter()?.value || '',
			height: 340,
		}),
		[
			selectedAgency,
			selectedBrand,
			selectedRegion,
			selectedWaves,
			getPrimaryFilter,
		]
	);

	const theme = useTheme();

	// Fetch initial filter options and dashboard stats
	useEffect(() => {
		const fetchInitialData = async () => {
			try {
				setLoading(true);

				// Fetch initial filter options
				const [
					brandsResponse,
					regionsResponse,
					agenciesResponse,
					wavesResponse,
				] = await Promise.all([
					fetch('/api/surveys/unique-brands'),
					fetch('/api/surveys/unique-regions'),
					fetch('/api/surveys/unique-agencies'),
					fetch('/api/waves?excludeSeed=true'),
				]);

				const [agencies, brands, regions, waves] = await Promise.all([
					agenciesResponse.json(),
					brandsResponse.json(),
					regionsResponse.json(),
					wavesResponse.json(),
				]);

				const waveOptions = waves.map((w: { name: string; displayLabel?: string; responseCount?: number }) => ({
					label: `${w.displayLabel || w.name} (${w.responseCount || 0} responses)`,
					value: w.name,
				}));
				const initialFilterOptions = {
					agencies: ['All Agencies', ...agencies],
					brands: ['All Brands', ...brands],
					regions: ['All Regions', ...regions],
					waves: waveOptions,
				};

				setFilterOptions(initialFilterOptions);
				setAvailableFilterOptions(initialFilterOptions);

				// Set latest wave as default selection
				if (waveOptions.length > 0) {
					const latestWave = waveOptions[0].value; // First item is newest since API sorts by createdAt: -1
					setSelectedWaves([latestWave]);
					setTempSelectedWaves([latestWave]); // Initialize temp state
				}
			} catch (error) {
				console.error('Error fetching dashboard data:', error);
			} finally {
				setLoading(false);
			}
		};

		fetchInitialData();
	}, []);

	// Update available filter options when selections change
	useEffect(() => {
		const updateAvailableOptions = async () => {
			try {
				const params = new URLSearchParams();

				// Always include selected waves as the primary filter
				if (selectedWaves.length > 0) {
					params.append('wave', selectedWaves.join(','));
				}

				if (selectedAgency !== 'All Agencies') {
					params.append('agency', selectedAgency);
				}
				if (selectedBrand !== 'All Brands') {
					params.append('brand', selectedBrand);
				}
				if (selectedRegion !== 'All Regions') {
					params.append('region', selectedRegion);
				}

				const response = await fetch(
					`/api/surveys/filtered-options?${params.toString()}`
				);
				const data = await response.json();

				setAvailableFilterOptions({
					agencies: data.agencies || filterOptions.agencies,
					brands: data.brands || filterOptions.brands,
					regions: data.regions || filterOptions.regions,
					waves: filterOptions.waves, // Waves don't filter based on other selections
				});
			} catch (error) {
				console.error('Error fetching filtered options:', error);
			}
		};

		// Only update if we have initial data and waves are selected
		if (filterOptions.agencies.length > 1 && selectedWaves.length > 0) {
			updateAvailableOptions();
		}
	}, [
		selectedWaves,
		selectedAgency,
		selectedBrand,
		selectedRegion,
		filterOptions,
	]);

	// Fetch wave-filtered stats when selectedWaves changes
	useEffect(() => {
		const fetchWaveFilteredStats = async () => {
			if (selectedWaves.length === 0) {
				setWaveFilteredStats(null);
				return;
			}

			try {
				const params = new URLSearchParams();
				params.append('wave', selectedWaves.join(','));

				const response = await fetch(
					`/api/responses/stats?${params.toString()}`
				);
				const stats = await response.json();
				setWaveFilteredStats(stats);
			} catch (error) {
				console.error('Error fetching wave-filtered stats:', error);
			}
		};

		fetchWaveFilteredStats();
	}, [selectedWaves]);

	// Fetch fully filtered stats when any filter changes
	useEffect(() => {
		const fetchFilteredStats = async () => {
			if (selectedWaves.length === 0) {
				setFilteredStats(null);
				return;
			}

			try {
				const params = new URLSearchParams();
				params.append('wave', selectedWaves.join(','));

				if (selectedAgency !== 'All Agencies') {
					params.append('agencyName', selectedAgency);
				}
				if (selectedBrand !== 'All Brands') {
					params.append('brand', selectedBrand);
				}
				if (selectedRegion !== 'All Regions') {
					params.append('region', selectedRegion);
				}

				const response = await fetch(
					`/api/responses/stats?${params.toString()}`
				);
				const stats = await response.json();
				setFilteredStats(stats);
			} catch (error) {
				console.error('Error fetching filtered stats:', error);
			}
		};

		fetchFilteredStats();
	}, [selectedWaves, selectedAgency, selectedBrand, selectedRegion]);

	const handleAgencyChange = (
		event: React.SyntheticEvent,
		value: string | null
	) => {
		setSelectedAgency(value || 'All Agencies');
	};

	const handleBrandChange = (
		event: React.SyntheticEvent,
		value: string | null
	) => {
		setSelectedBrand(value || 'All Brands');
	};

	const handleRegionChange = (
		event: React.SyntheticEvent,
		value: string | null
	) => {
		setSelectedRegion(value || 'All Regions');
	};

	const handleWaveChange = (event: SelectChangeEvent<string[]>) => {
		const value = event.target.value;
		setTempSelectedWaves(typeof value === 'string' ? value.split(',') : value);
	};

	const handleWaveClose = () => {
		setSelectedWaves(tempSelectedWaves);
	};

	const handleResetFilters = () => {
		setSelectedAgency('All Agencies');
		setSelectedBrand('All Brands');
		setSelectedRegion('All Regions');
	};

	if (loading) {
		return (
			<Box
				sx={{
					display: 'flex',
					justifyContent: 'center',
					alignItems: 'center',
					height: 400,
				}}
			>
				<CircularProgress />
			</Box>
		);
	}

	const primaryFilter = getPrimaryFilter();

	return (
		<Box>
			{/* Header with Dashboard title and Wave filter */}
			<Box sx={{
				display: 'flex',
				alignItems: 'flex-start',
				gap: 3,
				mb: 2,
				flexWrap: { xs: 'wrap', md: 'nowrap' }
			}}>
				<ElevatedFormControl
					size="small"
					sx={{
						width: { xs: '100%', md: '300px' },
						minWidth: '200px',
						flex: { xs: '1 1 100%', md: '0 0 auto' }
					}}
				>
					<InputLabel>Wave</InputLabel>
					<Select
						multiple
						value={tempSelectedWaves}
						onChange={handleWaveChange}
						onClose={handleWaveClose}
						input={<OutlinedInput label="Wave" />}
						renderValue={(selected) => renderWaveValue(selected, filterOptions)}
					>
						{filterOptions.waves.map((wave) => (
							<MenuItem key={wave.value} value={wave.value}>
								<Checkbox checked={tempSelectedWaves.indexOf(wave.value) > -1} />
								<ListItemText primary={wave.label} />
							</MenuItem>
						))}
					</Select>
				</ElevatedFormControl>
				<PanelSubheader
					sx={{
						lineHeight: '1rem',
						whiteSpace: 'nowrap',
						flex: '0 0 auto',
						minWidth: 'fit-content'
					}}
					gutterBottom
				>
					{waveFilteredStats && (
						<>
							{waveFilteredStats.totalResponses} responses
							<br />
							{waveFilteredStats.completionRate.toFixed(1)}% completion rate
							<br />
							{waveFilteredStats.averageScore.toFixed(1)} avg score
						</>
					)}
				</PanelSubheader>
			</Box>

			{/* Show message if no waves selected */}
			{selectedWaves.length === 0 && (
				<Box
					sx={{
						mt: 4,
						p: 3,
						textAlign: 'center',
						bgcolor: theme.palette.mode === 'dark' ? 'grey.900' : 'grey.100',
						borderRadius: 1,
					}}
				>
					<Typography variant="h6" color="text.secondary">
						No Wave Selected
					</Typography>
					<Typography color="text.secondary">
						Please select at least one wave to view dashboard data.
					</Typography>
				</Box>
			)}

			{/* Main content - only show if waves are selected */}
			{selectedWaves.length > 0 && (
				<Box sx={{ mt: 3, display: 'flex', flexDirection: 'column', gap: 3 }}>
					{/* Filters */}
					<Box
						sx={{
							display: 'flex',
							gap: 2,
							flexWrap: 'wrap',
							alignItems: 'center',
						}}
					>
						<ElevatedFormControl size="small">
							<Autocomplete
								options={availableFilterOptions.agencies}
								value={selectedAgency}
								onChange={handleAgencyChange}
								renderInput={(params) => (
									<TextField {...params} label="Agency" size="small" />
								)}
								size="small"
								sx={{ minWidth: 140 }}
								disableClearable
							/>
						</ElevatedFormControl>
						<ElevatedFormControl size="small">
							<Autocomplete
								options={availableFilterOptions.brands}
								value={selectedBrand}
								onChange={handleBrandChange}
								renderInput={(params) => (
									<TextField {...params} label="Brand" size="small" />
								)}
								size="small"
								sx={{ minWidth: 140 }}
								disableClearable
							/>
						</ElevatedFormControl>
						<ElevatedFormControl size="small">
							<Autocomplete
								options={availableFilterOptions.regions}
								value={selectedRegion}
								onChange={handleRegionChange}
								renderInput={(params) => (
									<TextField {...params} label="Region" size="small" />
								)}
								size="small"
								sx={{ minWidth: 140 }}
								disableClearable
							/>
						</ElevatedFormControl>
						{/* Filtered Stats Subtitle */}
						{filteredStats &&
							(selectedAgency !== 'All Agencies' ||
								selectedBrand !== 'All Brands' ||
								selectedRegion !== 'All Regions') && (
								<Typography
									variant="subtitle2"
									color="text.secondary"
									sx={{
										fontSize: '0.7rem',
										lineHeight: '0.8rem',
										whiteSpace: 'nowrap',
									}}
								>
									{filteredStats.totalResponses} responses
									<br />
									{filteredStats.completionRate.toFixed(1)}% completion rate
									<br />
									{filteredStats.averageScore.toFixed(1)} avg score
								</Typography>
							)}

						<Tooltip title="Reset all filters">
							<Button
								variant="text"
								size="small"
								onClick={handleResetFilters}
								startIcon={<ResetIcon />}
								disabled={
									selectedAgency === 'All Agencies' &&
									selectedBrand === 'All Brands' &&
									selectedRegion === 'All Regions'
								}
								sx={{ minWidth: 'auto', px: 2 }}
							>
								Reset Filters
							</Button>
						</Tooltip>
					</Box>

					{/* Row 1: Chart & Comments with synced heights */}
					<SyncedHeightPanels
						leftPanel={<DashboardBarChart {...chartProps} />}
						rightPanel={
							<CommentsSentimentsPanel
								focusType={primaryFilter?.type || 'agency'}
								focusValue={primaryFilter?.value || 'None'}
								wave={selectedWaves.join(',')}
							/>
						}
						showRightPanel={!!primaryFilter}
						gap={3}
					/>

					{/* Row 2: Drilldown, Wave History & NPS History - Only show when a filter is selected */}
					{primaryFilter && (
						<Box
							sx={{
								display: 'grid',
								gridTemplateColumns: '1fr 1fr 1fr',
								gap: 3,
								alignItems: 'flex-start',
							}}
						>
							<DrilldownPanel
								primaryFilter={primaryFilter.type}
								primaryValue={primaryFilter.value}
								wave={selectedWaves.join(',')} // Pass selected waves to drilldown panel
							/>
							<WaveHistoryPanel
								focusType={primaryFilter.type}
								focusValue={primaryFilter.value}
							/>
							<NPSHistoryPanel
								focusType={primaryFilter.type}
								focusValue={primaryFilter.value}
							/>
						</Box>
					)}
				</Box>
			)}
		</Box>
	);
}
