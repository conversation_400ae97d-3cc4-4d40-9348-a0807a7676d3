'use client';

import { Typography, Box, CircularProgress, useTheme } from '@mui/material';
import { LineChart } from '@mui/x-charts/LineChart';
import { useState, useEffect } from 'react';
import PanelHeader from './PanelHeader';
import PanelSubheader from './PanelSubheader';
import DashboardPanel from './DashboardPanel';

interface NPSHistoryPanelProps {
	focusType: 'agency' | 'brand' | 'region' | 'wave';
	focusValue: string;
}

interface ChartDataPoint {
	wave: string;
	nps: number;
	responseCount: number;
	[key: string]: string | number | undefined;
}

export default function NPSHistoryPanel({
	focusType,
	focusValue,
}: NPSHistoryPanelProps) {
	const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [responseCount, setResponseCount] = useState(0);
	const theme = useTheme();

	useEffect(() => {
		const fetchNPSHistory = async () => {
			try {
				setLoading(true);
				setError(null);

				const params = new URLSearchParams({
					focusType,
					focusValue,
				});

				const response = await fetch(
					`/api/responses/wave-history?${params.toString()}`
				);

				if (!response.ok) {
					throw new Error('Failed to fetch NPS history');
				}

				const data = await response.json();
				const waveNames = data.waveNames || [];
				const npsScores = data.npsScores || [];
				const responseCounts = data.responseCounts || [];

				// Check if we have NPS data - NPS can be negative or 0, so check for array length and non-null values
				const hasNPS =
					npsScores.length > 0 &&
					npsScores.some(
						(nps: number | null | undefined) =>
							nps !== null && nps !== undefined
					);

				if (!hasNPS) {
					setChartData([]);
					setResponseCount(data.responseCount || 0);
					return;
				}

				// Transform data for MUI X Charts - include NPS data and response counts
				const transformedData: ChartDataPoint[] = waveNames.map(
					(wave: string, index: number) => ({
						wave,
						nps: npsScores[index] || 0,
						responseCount: responseCounts[index] || 0,
					})
				);

				setChartData(transformedData);
				setResponseCount(data.responseCount || 0);
			} catch (err) {
				console.error('Error fetching NPS history:', err);
				setError('Failed to load NPS history');
				setChartData([]);
			} finally {
				setLoading(false);
			}
		};

		fetchNPSHistory();
	}, [focusType, focusValue]);

	// If no data, show a message
	if (!loading && chartData.length === 0) {
		return (
			<DashboardPanel sx={{ p: 2, bgcolor: '#e3f2fd', height: 300 }}>
				<PanelHeader gutterBottom>{focusValue} NPS History</PanelHeader>
				<Box
					sx={{
						display: 'flex',
						flexDirection: 'column',
						alignItems: 'center',
						justifyContent: 'center',
						height: 200,
					}}
				>
					<Typography variant="body2" color="text.secondary" textAlign="center">
						No NPS history data available for {focusValue}.
					</Typography>
				</Box>
			</DashboardPanel>
		);
	}

	return (
		<DashboardPanel sx={{ p: 2, textAlign: 'center' }}>
			<PanelHeader>{focusValue} NPS History</PanelHeader>
			<PanelSubheader gutterBottom sx={{ mb: 1 }}>
				Net Promoter Score (-1 to 1)
			</PanelSubheader>

			{loading ? (
				<Box
					sx={{
						display: 'flex',
						alignItems: 'center',
						justifyContent: 'center',
						height: 200,
					}}
				>
					<CircularProgress size={24} />
				</Box>
			) : (
				<Box
					sx={{
						display: 'flex',
						flexDirection: 'column',
						alignItems: 'center',
						gap: 2,
						textAlign: 'center',
					}}
				>
					{error && (
						<Typography variant="body2" color="error" sx={{ mb: 1 }}>
							{error}
						</Typography>
					)}

					{/* MUI X Charts LineChart for NPS */}
					<Box
						sx={{
							width: '100%',
							position: 'relative',
							textAlign: 'center',
						}}
					>
						<LineChart
							dataset={chartData}
							xAxis={[
								{
									scaleType: 'point',
									dataKey: 'wave',
									tickLabelStyle: {
										display: 'none',
										fontSize: 10,
										fill: theme.palette.text.secondary,
									},
								},
							]}
							yAxis={[
								{
									min: -1,
									max: 1,
									tickNumber: 5,
									tickLabelStyle: {
										display: 'none',
										fontSize: 10,
										fill: theme.palette.text.secondary,
									},
								},
							]}
							series={[
								{
									dataKey: 'nps',
									label: 'NPS Score',
									color: theme.palette.secondary.main,
									curve: 'natural' as const,
									showMark: true,
									area: false,
									valueFormatter: (value: number | null, context) => {
										if (value === null) return '';
										const dataIndex = context?.dataIndex;
										if (typeof dataIndex === 'number' && chartData[dataIndex]) {
											const responseCount = chartData[dataIndex].responseCount;
											return `${value} (${responseCount} responses)`;
										}
										return value.toString();
									},
								},
							]}
							height={240}
							margin={{ left: 30, right: 20, top: 30, bottom: 30 }}
							slots={{
								mark: ({ x, y, color, dataIndex }) => {
									if (
										typeof x !== 'number' ||
										typeof y !== 'number' ||
										typeof dataIndex !== 'number'
									) {
										return null;
									}
									const value = chartData[dataIndex]?.nps;

									return (
										<g key={`nps-${dataIndex}`}>
											<circle
												cx={x}
												cy={y}
												r={4}
												fill={`${color}80`}
												stroke={color}
												strokeWidth={2}
												style={{
													filter: 'drop-shadow(0px 2px 4px rgba(0,0,0,0.2))',
												}}
											/>
											<text
												x={x}
												y={y - 12}
												textAnchor="middle"
												fontSize={11}
												fontWeight="bold"
												fill={color}
											>
												{value}
											</text>
										</g>
									);
								},
							}}
							sx={{
								border: '1px solid rgba(0, 0, 0, 0.05)',
								borderRadius: 2,
								'& .MuiChartsAxis-tick': {
									stroke: 'rgba(0, 0, 0, 0.1)',
								},
								'& .MuiChartsAxis-line': {
									stroke: 'rgba(0, 0, 0, 0.1)',
								},
								'& .MuiLineElement-root': {
									strokeWidth: 3,
								},
							}}
						/>
					</Box>

					{/* Current trend indicator */}
					{chartData.length > 1 && (
						<Box
							sx={{
								textAlign: 'center',
								borderRadius: 1,
							}}
						>
							<Box
								sx={{
									display: 'flex',
									justifyContent: 'center',
									gap: 3,
									mb: 1,
								}}
							>
								<Box>
									<Typography variant="body2" color="text.primary">
										Latest NPS:{' '}
										<strong style={{ color: theme.palette.secondary.main }}>
											{chartData[chartData.length - 1]?.nps}
										</strong>
									</Typography>
									<Typography
										variant="body2"
										sx={{
											color: (() => {
												const currentNPS = chartData[chartData.length - 1]?.nps || 0;

												// Calculate rolling 3-wave average as reference point
												// Use the 3 waves before the current wave, or all available waves if less than 4 total
												let referenceAverage: number;
												if (chartData.length >= 4) {
													// Use the 3 waves before the current (last) wave
													const referenceWaves = chartData.slice(-4, -1);
													referenceAverage = referenceWaves.reduce((sum, wave) => sum + (wave.nps || 0), 0) / referenceWaves.length;
												} else if (chartData.length >= 2) {
													// Use all waves except the current one
													const referenceWaves = chartData.slice(0, -1);
													referenceAverage = referenceWaves.reduce((sum, wave) => sum + (wave.nps || 0), 0) / referenceWaves.length;
												} else {
													// Only one wave, can't determine trend
													return theme.palette.text.secondary;
												}

												const difference = currentNPS - referenceAverage;
												const threshold = 0.1;

												if (difference > threshold) {
													return theme.palette.success.main; // Improving
												} else if (difference < -threshold) {
													return theme.palette.warning.main; // Declining
												} else {
													return theme.palette.text.secondary; // Stable
												}
											})(),
											fontWeight: 600,
										}}
									>
										{(() => {
											const currentNPS = chartData[chartData.length - 1]?.nps || 0;

											// Calculate rolling 3-wave average as reference point
											let referenceAverage: number;
											if (chartData.length >= 4) {
												// Use the 3 waves before the current (last) wave
												const referenceWaves = chartData.slice(-4, -1);
												referenceAverage = referenceWaves.reduce((sum, wave) => sum + (wave.nps || 0), 0) / referenceWaves.length;
											} else if (chartData.length >= 2) {
												// Use all waves except the current one
												const referenceWaves = chartData.slice(0, -1);
												referenceAverage = referenceWaves.reduce((sum, wave) => sum + (wave.nps || 0), 0) / referenceWaves.length;
											} else {
												// Only one wave, can't determine trend
												return '→ Stable';
											}

											const difference = currentNPS - referenceAverage;
											const threshold = 0.1;

											if (difference > threshold) {
												return '↗ Improving';
											} else if (difference < -threshold) {
												return '↘ Declining';
											} else {
												return '→ Stable';
											}
										})()}
									</Typography>
								</Box>
							</Box>

							<Typography variant="caption" color="text.secondary">
								Based on {responseCount} responses
							</Typography>
						</Box>
					)}
				</Box>
			)}
		</DashboardPanel>
	);
}
