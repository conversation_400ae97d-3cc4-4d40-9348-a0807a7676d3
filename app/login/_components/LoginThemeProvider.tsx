'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Icon<PERSON>utton, Tooltip, Box } from '@mui/material';
import { DarkMode, LightMode } from '@mui/icons-material';

interface LoginThemeContextType {
  darkMode: boolean;
  toggleDarkMode: () => void;
}

const LoginThemeContext = createContext<LoginThemeContextType | undefined>(undefined);

export const useLoginTheme = () => {
  const context = useContext(LoginThemeContext);
  if (!context) {
    throw new Error('useLoginTheme must be used within a LoginThemeProvider');
  }
  return context;
};

interface LoginThemeProviderProps {
  children: React.ReactNode;
}

export default function LoginThemeProvider({
  children,
}: LoginThemeProviderProps) {
  const [darkMode, setDarkMode] = useState(false);

  // Load theme preference from localStorage on mount
  useEffect(() => {
    const savedTheme = localStorage.getItem('loginDarkMode');
    if (savedTheme !== null) {
      setDarkMode(JSON.parse(savedTheme));
    } else {
      // Default to light mode for login page
      setDarkMode(false);
    }
  }, []);

  // Save theme preference to localStorage
  useEffect(() => {
    localStorage.setItem('loginDarkMode', JSON.stringify(darkMode));
  }, [darkMode]);

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
  };

  const theme = createTheme({
    palette: {
      mode: darkMode ? 'dark' : 'light',
      primary: {
        main: '#E6B800', // Gold from logo
        light: '#F4D03F', // Lighter gold
        dark: '#B8860B', // Darker gold
        contrastText: '#000',
      },
      secondary: {
        main: '#e65100', // Darker orange for better contrast
        light: '#ff8f00', // Medium orange
        dark: '#bf360c', // Very dark orange
        contrastText: '#fff',
      },
    },
    typography: {
      fontFamily:
        '"Encode Sans Condensed", "Roboto", "Helvetica", "Arial", sans-serif',
    },
    components: {
      // Ensure the body background changes with theme
      MuiCssBaseline: {
        styleOverrides: {
          body: {
            backgroundColor: darkMode ? '#121212' : '#ffffff',
            transition: 'background-color 0.3s ease',
          },
        },
      },
    },
  });

  return (
    <LoginThemeContext.Provider value={{ darkMode, toggleDarkMode }}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        {/* Theme Toggle Button */}
        <Box
          sx={{
            position: 'fixed',
            top: 16,
            right: 16,
            zIndex: 1000,
          }}
        >
          <Tooltip title={darkMode ? 'Switch to light mode' : 'Switch to dark mode'}>
            <IconButton onClick={toggleDarkMode} color="inherit">
              {darkMode ? <LightMode /> : <DarkMode />}
            </IconButton>
          </Tooltip>
        </Box>
        {children}
      </ThemeProvider>
    </LoginThemeContext.Provider>
  );
}
